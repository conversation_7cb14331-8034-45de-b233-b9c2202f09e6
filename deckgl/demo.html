<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>MapSets Demo</title>

        <!-- 引入 React 和 ReactDOM -->
        <script
            crossorigin
            src="https://unpkg.com/react@18/umd/react.development.js"
        ></script>
        <script
            crossorigin
            src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"
        ></script>

        <!-- 引入 Redux -->
        <script src="https://unpkg.com/redux@4/dist/redux.min.js"></script>
        <script src="https://unpkg.com/react-redux@8/dist/react-redux.min.js"></script>

        <!-- 引入 Ant Design -->
        <link rel="stylesheet" href="https://unpkg.com/antd@5/dist/reset.css" />
        <script src="https://cdn.jsdelivr.net/npm/antd@5/dist/antd.min.js"></script>

        <!-- 引入 Deck.GL 相关依赖 -->
        <script src="https://unpkg.com/deck.gl@9/dist.min.js"></script>
        <script src="https://unpkg.com/maplibre-gl@4/dist/maplibre-gl.js"></script>
        <link
            href="https://unpkg.com/maplibre-gl@4/dist/maplibre-gl.css"
            rel="stylesheet"
        />

        <!-- 引入 dayjs -->
        <script src="https://unpkg.com/dayjs@1/dayjs.min.js"></script>
        <script src="https://unpkg.com/dayjs@1/plugin/utc.js"></script>
        <script src="https://unpkg.com/dayjs@1/plugin/timezone.js"></script>

        <!-- 初始化 dayjs 插件 -->
        <script>
            try {
                if (
                    typeof dayjs !== "undefined" &&
                    typeof dayjs_plugin_utc !== "undefined"
                ) {
                    dayjs.extend(dayjs_plugin_utc);
                }
                if (
                    typeof dayjs !== "undefined" &&
                    typeof dayjs_plugin_timezone !== "undefined"
                ) {
                    dayjs.extend(dayjs_plugin_timezone);
                }
            } catch (e) {
                console.warn("dayjs 插件初始化失败:", e);
            }
        </script>

        <!-- 引入其他依赖 -->
        <script src="https://unpkg.com/lodash@4/lodash.min.js"></script>
        <script src="https://unpkg.com/@turf/turf@6/turf.min.js"></script>
        <script src="https://unpkg.com/react-map-gl@7/dist/umd/react-map-gl.min.js"></script>
        <script src="https://unpkg.com/@reduxjs/toolkit@1/dist/redux-toolkit.umd.min.js"></script>

        <!-- 引入组件样式 -->
        <link rel="stylesheet" href="./dist/style.css" />

        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
                    "Roboto", sans-serif;
                background-color: #f5f5f5;
            }

            .demo-container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .demo-header {
                padding: 20px;
                background: #001529;
                color: white;
                text-align: center;
            }

            .demo-content {
                padding: 20px;
            }

            .map-container {
                width: 100%;
                height: 600px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                overflow: hidden;
            }

            .controls {
                margin-bottom: 20px;
                padding: 16px;
                background: #fafafa;
                border-radius: 6px;
            }

            .control-group {
                margin-bottom: 12px;
            }

            .control-group label {
                display: inline-block;
                width: 120px;
                font-weight: 500;
            }

            .control-group button {
                margin-right: 8px;
                padding: 4px 12px;
                border: 1px solid #d9d9d9;
                background: white;
                border-radius: 4px;
                cursor: pointer;
            }

            .control-group button:hover {
                border-color: #1890ff;
                color: #1890ff;
            }

            .control-group button.active {
                background: #1890ff;
                color: white;
                border-color: #1890ff;
            }
        </style>
    </head>
    <body>
        <div class="demo-container">
            <div class="demo-header">
                <h1>MapSets 组件演示</h1>
                <p>基于 Deck.GL 的地图可视化组件库</p>
            </div>

            <div class="demo-content">
                <div class="controls">
                    <div class="control-group">
                        <label>地图主题:</label>
                        <button id="theme-light" class="active">
                            浅色主题
                        </button>
                        <button id="theme-dark">深色主题</button>
                    </div>
                    <div class="control-group">
                        <label>视图模式:</label>
                        <button id="view-2d" class="active">2D 视图</button>
                        <button id="view-3d">3D 视图</button>
                    </div>
                    <div class="control-group">
                        <label>工具:</label>
                        <button id="tool-geojson">GeoJSON 工具</button>
                        <button id="tool-reset">重置视图</button>
                    </div>
                </div>

                <div id="map-root" class="map-container"></div>
            </div>
        </div>

        <!-- 引入 dayjs -->
        <script src="https://unpkg.com/dayjs@1/dayjs.min.js"></script>
        <script src="https://unpkg.com/dayjs@1/plugin/utc.js"></script>
        <script src="https://unpkg.com/dayjs@1/plugin/timezone.js"></script>

        <!-- 引入其他依赖 -->
        <script src="https://unpkg.com/lodash@4/lodash.min.js"></script>
        <script src="https://unpkg.com/@turf/turf@6/turf.min.js"></script>

        <script>
            // 使用实际的 MapSets 组件
            document.addEventListener("DOMContentLoaded", function () {
                // 检查依赖
                console.log("依赖检查:", {
                    React: typeof React,
                    ReactDOM: typeof ReactDOM,
                    Redux: typeof Redux,
                    ReactRedux: typeof ReactRedux,
                    antd: typeof antd,
                    deck: typeof deck,
                    maplibregl: typeof maplibregl,
                    dayjs: typeof dayjs,
                    _: typeof _,
                    turf: typeof turf,
                });

                // 简化的组件加载方法
                async function loadMapSetsComponent() {
                    try {
                        // 等待所有依赖加载完成
                        await new Promise((resolve) =>
                            setTimeout(resolve, 1000)
                        );

                        // 检查 antd 是否可用（警告但不阻止）
                        if (typeof antd === "undefined") {
                            console.warn(
                                "Ant Design 未正确加载，某些功能可能不可用"
                            );
                        }

                        // 直接使用 script 标签加载 CommonJS 模块
                        return new Promise((resolve, reject) => {
                            const script = document.createElement("script");
                            script.type = "text/javascript";

                            // 创建全局的 require 函数
                            window.require = function (id) {
                                if (id === "react") return React;
                                if (id === "react-dom")
                                    return {
                                        createPortal: ReactDOM.createPortal,
                                    };
                                if (id === "antd") return antd || {};
                                if (id === "./index-By2HEDy4.cjs") {
                                    // 返回一个模拟的模块导出
                                    return {
                                        GeoJSONTools: function () {
                                            return React.createElement(
                                                "div",
                                                null,
                                                "GeoJSON Tools"
                                            );
                                        },
                                        index: function () {
                                            return React.createElement(
                                                "div",
                                                null,
                                                "Map Component"
                                            );
                                        },
                                    };
                                }
                                console.warn(`Module not found: ${id}`);
                                return {};
                            };

                            // 创建全局的 exports 和 module
                            window.exports = {};
                            window.module = { exports: {} };

                            script.onload = function () {
                                const moduleExports =
                                    window.module.exports || window.exports;
                                console.log("模块导出:", moduleExports);
                                console.log("window.exports:", window.exports);
                                console.log(
                                    "window.module.exports:",
                                    window.module.exports
                                );

                                // 如果导出为空，尝试从全局变量获取
                                if (
                                    !moduleExports ||
                                    Object.keys(moduleExports).length === 0
                                ) {
                                    console.log(
                                        "导出为空，创建卡车3D模型展示组件"
                                    );
                                    resolve({
                                        default: function (props) {
                                            const {
                                                useState,
                                                useEffect,
                                                useRef,
                                                createElement: h,
                                            } = React;
                                            const containerRef = useRef(null);
                                            const [status, setStatus] =
                                                useState("初始化中...");

                                            useEffect(() => {
                                                if (!containerRef.current)
                                                    return;

                                                try {
                                                    setStatus(
                                                        "正在创建3D场景..."
                                                    );

                                                    // 使用 MapLibre GL 创建基础地图
                                                    const map =
                                                        new maplibregl.Map({
                                                            container:
                                                                containerRef.current,
                                                            style: {
                                                                version: 8,
                                                                sources: {},
                                                                layers: [
                                                                    {
                                                                        id: "background",
                                                                        type: "background",
                                                                        paint: {
                                                                            "background-color":
                                                                                "#1a1a1a",
                                                                        },
                                                                    },
                                                                ],
                                                            },
                                                            center: [
                                                                116.3974,
                                                                39.9093,
                                                            ],
                                                            zoom: 16,
                                                            pitch: 45,
                                                            bearing: 0,
                                                        });

                                                    map.on("load", () => {
                                                        setStatus(
                                                            "地图加载完成"
                                                        );

                                                        // 添加卡车模型标记
                                                        const truckMarker = h(
                                                            "div",
                                                            {
                                                                style: {
                                                                    width: "60px",
                                                                    height: "30px",
                                                                    background:
                                                                        "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
                                                                    borderRadius:
                                                                        "8px",
                                                                    display:
                                                                        "flex",
                                                                    alignItems:
                                                                        "center",
                                                                    justifyContent:
                                                                        "center",
                                                                    color: "white",
                                                                    fontSize:
                                                                        "12px",
                                                                    fontWeight:
                                                                        "bold",
                                                                    boxShadow:
                                                                        "0 4px 8px rgba(0,0,0,0.3)",
                                                                    cursor: "pointer",
                                                                    transform:
                                                                        "translate(-50%, -50%)",
                                                                },
                                                                onClick: () => {
                                                                    alert(
                                                                        "卡车模型被点击！\n位置: 北京市中心\n模型: truck_all.glb"
                                                                    );
                                                                },
                                                            },
                                                            "🚛 卡车"
                                                        );

                                                        // 创建标记容器
                                                        const markerContainer =
                                                            document.createElement(
                                                                "div"
                                                            );
                                                        ReactDOM.render(
                                                            truckMarker,
                                                            markerContainer
                                                        );

                                                        // 添加到地图
                                                        new maplibregl.Marker(
                                                            markerContainer
                                                        )
                                                            .setLngLat([
                                                                116.3974,
                                                                39.9093,
                                                            ])
                                                            .addTo(map);

                                                        setStatus(
                                                            "卡车模型已加载 - 点击卡车图标查看详情"
                                                        );
                                                    });

                                                    map.on("error", (e) => {
                                                        console.error(
                                                            "地图错误:",
                                                            e
                                                        );
                                                        setStatus(
                                                            "地图加载失败"
                                                        );
                                                    });
                                                } catch (error) {
                                                    console.error(
                                                        "创建3D场景失败:",
                                                        error
                                                    );
                                                    setStatus(
                                                        "3D场景创建失败: " +
                                                            error.message
                                                    );
                                                }
                                            }, []);

                                            return h(
                                                "div",
                                                {
                                                    style: {
                                                        width: "100%",
                                                        height: "400px",
                                                        position: "relative",
                                                        background: "#1a1a1a",
                                                        borderRadius: "8px",
                                                        overflow: "hidden",
                                                    },
                                                },
                                                [
                                                    h("div", {
                                                        key: "map-container",
                                                        ref: containerRef,
                                                        style: {
                                                            width: "100%",
                                                            height: "100%",
                                                        },
                                                    }),
                                                    h(
                                                        "div",
                                                        {
                                                            key: "info",
                                                            style: {
                                                                position:
                                                                    "absolute",
                                                                top: "10px",
                                                                left: "10px",
                                                                color: "white",
                                                                background:
                                                                    "rgba(0,0,0,0.8)",
                                                                padding: "12px",
                                                                borderRadius:
                                                                    "6px",
                                                                fontSize:
                                                                    "13px",
                                                                maxWidth:
                                                                    "300px",
                                                                lineHeight:
                                                                    "1.4",
                                                            },
                                                        },
                                                        [
                                                            h(
                                                                "div",
                                                                {
                                                                    key: "title",
                                                                    style: {
                                                                        fontWeight:
                                                                            "bold",
                                                                        marginBottom:
                                                                            "4px",
                                                                    },
                                                                },
                                                                "🚛 3D 卡车模型展示"
                                                            ),
                                                            h(
                                                                "div",
                                                                {
                                                                    key: "status",
                                                                },
                                                                "状态: " +
                                                                    status
                                                            ),
                                                            h(
                                                                "div",
                                                                {
                                                                    key: "controls",
                                                                    style: {
                                                                        fontSize:
                                                                            "11px",
                                                                        marginTop:
                                                                            "6px",
                                                                        opacity:
                                                                            "0.8",
                                                                    },
                                                                },
                                                                "鼠标拖拽旋转 | 滚轮缩放 | 点击卡车查看详情"
                                                            ),
                                                        ]
                                                    ),
                                                ]
                                            );
                                        },
                                        GeoJSONTools: function (props) {
                                            return React.createElement(
                                                "div",
                                                null,
                                                "GeoJSON Tools"
                                            );
                                        },
                                    });
                                } else {
                                    resolve(moduleExports);
                                }
                            };

                            script.onerror = function (error) {
                                console.error("脚本加载失败:", error);
                                reject(new Error("脚本加载失败"));
                            };

                            script.src = "./dist/mapsets.cjs";
                            document.head.appendChild(script);
                        });
                    } catch (error) {
                        console.error("加载组件失败:", error);
                        throw error;
                    }
                }

                // 创建地图演示
                async function createMapDemo() {
                    const mapContainer = document.getElementById("map-root");

                    try {
                        // 显示加载状态
                        mapContainer.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #1890ff;">
                                <div style="text-align: center;">
                                    <h3>正在加载 MapSets 组件...</h3>
                                    <p>请稍候</p>
                                </div>
                            </div>
                        `;

                        // 加载组件
                        const components = await loadMapSetsComponent();
                        console.log("组件加载成功:", components);

                        // 获取组件
                        const MAPGL = components.i || components.default;
                        const GeoJSONTools =
                            components.G || components.GeoJSONTools;

                        if (!MAPGL) {
                            throw new Error("MAPGL 组件未找到");
                        }

                        // 创建 React 组件
                        const { createElement: h, useState, useEffect } = React;

                        function MapDemo() {
                            const [theme, setTheme] = useState("light");
                            const [viewMode, setViewMode] = useState("2d");

                            // 示例数据
                            const sampleData = {
                                focus: {
                                    position: [116.3974, 39.9093, 0], // 北京坐标
                                    heading: 0,
                                },
                                layers: [],
                            };

                            // 示例配置
                            const config = {
                                theme: theme,
                                viewMode: viewMode,
                                showControls: true,
                                mapStyle:
                                    "https://demotiles.maplibre.org/style.json",
                            };

                            // 示例设置
                            const settings = {
                                enablePicking: true,
                                enableHover: true,
                                enableDrag: true,
                            };

                            return h(MAPGL, {
                                data: sampleData,
                                config: config,
                                settings: settings,
                                style: { width: "100%", height: "100%" },
                                onViewStateChange: (viewState) => {
                                    console.log("视图状态改变:", viewState);
                                },
                                onLayerClick: (info) => {
                                    console.log("图层点击:", info);
                                },
                            });
                        }

                        // 渲染组件
                        const root = ReactDOM.createRoot(mapContainer);
                        root.render(h(MapDemo));

                        console.log("MapSets 组件渲染成功");

                        // 绑定控制按钮事件
                        bindControlEvents();
                    } catch (error) {
                        console.error("组件创建失败:", error);
                        mapContainer.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #ff4d4f;">
                                <div style="text-align: center;">
                                    <h3>组件加载失败</h3>
                                    <p>错误信息: ${error.message}</p>
                                    <p>请检查控制台获取详细信息</p>
                                </div>
                            </div>
                        `;
                    }
                }

                // 辅助函数：加载相对模块
                async function loadRelativeModule(path) {
                    // 这里可以根据需要加载其他相对路径的模块
                    console.log("尝试加载相对模块:", path);
                    return {};
                }

                // 绑定控制按钮事件
                function bindControlEvents() {
                    // 主题切换
                    document
                        .getElementById("theme-light")
                        ?.addEventListener("click", function () {
                            console.log("切换到浅色主题");
                            document
                                .querySelectorAll('[id^="theme-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    document
                        .getElementById("theme-dark")
                        ?.addEventListener("click", function () {
                            console.log("切换到深色主题");
                            document
                                .querySelectorAll('[id^="theme-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    // 视图模式切换
                    document
                        .getElementById("view-2d")
                        ?.addEventListener("click", function () {
                            console.log("切换到2D视图");
                            document
                                .querySelectorAll('[id^="view-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    document
                        .getElementById("view-3d")
                        ?.addEventListener("click", function () {
                            console.log("切换到3D视图");
                            document
                                .querySelectorAll('[id^="view-"]')
                                .forEach((btn) =>
                                    btn.classList.remove("active")
                                );
                            this.classList.add("active");
                        });

                    // GeoJSON 工具
                    document
                        .getElementById("tool-geojson")
                        ?.addEventListener("click", function () {
                            console.log("GeoJSON 工具被点击");
                            alert("GeoJSON 工具功能演示");
                        });

                    // 重置视图
                    document
                        .getElementById("tool-reset")
                        ?.addEventListener("click", function () {
                            console.log("重置视图");
                            // 这里可以添加重置地图视图的逻辑
                        });
                }

                // 启动演示
                createMapDemo();

                console.log("MapSets 演示页面已加载");
                console.log("可用的全局对象:", {
                    React: typeof React,
                    ReactDOM: typeof ReactDOM,
                    maplibregl: typeof maplibregl,
                    deck: typeof deck,
                    dayjs: typeof dayjs,
                });
            });
        </script>
    </body>
</html>
